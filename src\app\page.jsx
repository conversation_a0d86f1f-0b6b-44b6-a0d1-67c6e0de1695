import {
    ChevronDown,
    ChevronRight,
    Edit3,
    <PERSON><PERSON>,
    Minus,
    MoreHorizontal,
    Square,
    Type,
    X
} from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Footer from "../components/Footer";
import AIPanel from "./components/AIPanel";
import CanvasGrid from "./components/CanvasGrid";
import TopToolbar from "./components/TopToolbar";

export default function ASCIIMaker() {
  const [canvasWidth, setCanvasWidth] = useState(80);
  const [canvasHeight, setCanvasHeight] = useState(24);
  const [canvas, setCanvas] = useState([]);
  const [selectedTool, setSelectedTool] = useState("brush");
  const [selectedChar, setSelectedChar] = useState("█");
  const [isDrawing, setIsDrawing] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [cursorPos, setCursorPos] = useState({ x: 0, y: 0 });
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  
  // Enhanced paint tools state
  const [brushSize, setBrushSize] = useState(1);
  const [fillColor, setFillColor] = useState("█");
  const [lineStart, setLineStart] = useState(null);
  const [rectStart, setRectStart] = useState(null);
  const [isShiftPressed, setIsShiftPressed] = useState(false);
  const [clipboard, setClipboard] = useState(null);
  const [selection, setSelection] = useState(null);
  
  // Responsive sidebar state
  const [showLeftSidebar, setShowLeftSidebar] = useState(true);
  const [sidebarCompact, setSidebarCompact] = useState(false); // Compact sidebar mode
  const [isMobile, setIsMobile] = useState(false);
  const [expandedCharSets, setExpandedCharSets] = useState({});
  const [collapsedSections, setCollapsedSections] = useState({
    tools: false,
    palette: false,
    shortcuts: true, // Start with shortcuts collapsed to save space
    canvasSettings: false // Add canvas settings to collapsible sections
  });

  // Canvas responsive sizing
  const [canvasScale, setCanvasScale] = useState(1);
  const [cellSize, setCellSize] = useState({ width: 8, height: 16 });
  const [viewportSize, setViewportSize] = useState({ width: 1024, height: 768 });

  // AI Assistant states
  const [showAI, setShowAI] = useState(false);
  const [aiProvider, setAiProvider] = useState('openai'); // 'openai' or 'anthropic'
  const [aiInput, setAiInput] = useState("");
  const [aiLoading, setAiLoading] = useState(false);

  // AI Panel docking states
  const [aiDockMode, setAiDockMode] = useState('floating'); // 'floating', 'left', 'right'
  const [aiPanelWidth, setAiPanelWidth] = useState(400); // Width when docked
  const [isResizing, setIsResizing] = useState(false);

  // Chat thread management
  const [chatThreads, setChatThreads] = useState({});
  const [currentThreadId, setCurrentThreadId] = useState(null);
  const [nextThreadId, setNextThreadId] = useState(1);

  // Initialize default chat thread
  useEffect(() => {
    const savedThreads = localStorage.getItem('ai-chat-threads');
    const savedCurrentThread = localStorage.getItem('ai-current-thread');
    const savedNextId = localStorage.getItem('ai-next-thread-id');

    if (savedThreads) {
      const threads = JSON.parse(savedThreads);
      setChatThreads(threads);
      setCurrentThreadId(savedCurrentThread || Object.keys(threads)[0] || null);
      setNextThreadId(parseInt(savedNextId) || Object.keys(threads).length + 1);
    } else {
      // Create initial thread
      const initialThread = {
        id: '1',
        title: 'New Chat',
        createdAt: new Date().toISOString(),
        messages: [
          {
            role: "assistant",
            content: "Hi! I can help you create ASCII art. Try asking me to generate art from text, or ask for tips!",
          },
        ],
      };
      setChatThreads({ '1': initialThread });
      setCurrentThreadId('1');
      setNextThreadId(2);
    }
  }, []);

  // Save to localStorage whenever threads change
  useEffect(() => {
    if (Object.keys(chatThreads).length > 0) {
      localStorage.setItem('ai-chat-threads', JSON.stringify(chatThreads));
      localStorage.setItem('ai-current-thread', currentThreadId);
      localStorage.setItem('ai-next-thread-id', nextThreadId.toString());
    }
  }, [chatThreads, currentThreadId, nextThreadId]);

  // Get current thread messages
  const aiMessages = currentThreadId && chatThreads[currentThreadId]
    ? chatThreads[currentThreadId].messages
    : [];

  // Update messages in current thread
  const setAiMessages = useCallback((updater) => {
    if (!currentThreadId) return;

    setChatThreads(prev => {
      const newThreads = { ...prev };
      const currentThread = newThreads[currentThreadId];
      if (currentThread) {
        const newMessages = typeof updater === 'function'
          ? updater(currentThread.messages)
          : updater;
        newThreads[currentThreadId] = {
          ...currentThread,
          messages: newMessages,
          updatedAt: new Date().toISOString(),
        };
      }
      return newThreads;
    });
  }, [currentThreadId]);

  const canvasRef = useRef(null);
  const nextCanvasRef = useRef(null);
  const commitRafIdRef = useRef(0);
  const fpsRafIdRef = useRef(0);
  const drawOpsRef = useRef(0);
  const [drawOpsPerSec, setDrawOpsPerSec] = useState(0);
  const [fps, setFps] = useState(0);

  const scheduleCommit = useCallback(() => {
    if (commitRafIdRef.current) return;
    commitRafIdRef.current = requestAnimationFrame(() => {
      commitRafIdRef.current = 0;
      if (nextCanvasRef.current) {
        setCanvas(nextCanvasRef.current);
      }
    });
  }, [setCanvas]);

  const flushPending = useCallback(() => {
    if (commitRafIdRef.current) {
      cancelAnimationFrame(commitRafIdRef.current);
      commitRafIdRef.current = 0;
    }
    if (nextCanvasRef.current) {
      setCanvas(nextCanvasRef.current);
    }
  }, []);

  useEffect(() => {
    let mounted = true;
    let last = performance.now();
    let frames = 0;
    const loop = (now) => {
      frames += 1;
      if (now - last >= 1000) {
        if (mounted) setFps(frames);
        frames = 0;
        last = now;
        if (mounted) setDrawOpsPerSec(drawOpsRef.current);
        drawOpsRef.current = 0;
      }
      fpsRafIdRef.current = requestAnimationFrame(loop);
    };
    fpsRafIdRef.current = requestAnimationFrame(loop);
    return () => {
      mounted = false;
      if (fpsRafIdRef.current) cancelAnimationFrame(fpsRafIdRef.current);
      fpsRafIdRef.current = 0;
    };
  }, []);

  // ASCII character sets
  const charSets = {
    basic: [
      "█",
      "▓",
      "▒",
      "░",
      "■",
      "□",
      "▪",
      "▫",
      "●",
      "○",
      "▬",
      "─",
      "│",
      "┌",
      "┐",
      "└",
      "┘",
    ],
    drawing: [
      "╔",
      "╗",
      "╚",
      "╝",
      "║",
      "═",
      "╠",
      "╣",
      "╦",
      "╩",
      "╬",
      "┼",
      "├",
      "┤",
      "┬",
      "┴",
    ],
    symbols: [
      "★",
      "☆",
      "♠",
      "♣",
      "♥",
      "♦",
      "♪",
      "♫",
      "☀",
      "☁",
      "☂",
      "☃",
      "⚡",
      "⭐",
      "✓",
      "✗",
    ],
    numbers: [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "+",
      "-",
      "=",
      "*",
      "/",
      "%",
    ],
    letters: [
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
    ],
  };

  // Initialize canvas
  useEffect(() => {
    const newCanvas = Array(canvasHeight)
      .fill(null)
      .map(() => Array(canvasWidth).fill(" "));
    setCanvas(newCanvas);
    addToHistory(newCanvas);
  }, [canvasWidth, canvasHeight]);

  const addToHistory = (newCanvas) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(newCanvas)));
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setCanvas(JSON.parse(JSON.stringify(history[historyIndex - 1])));
    }
  };

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setCanvas(JSON.parse(JSON.stringify(history[historyIndex + 1])));
    }
  };

  const handleCanvasClick = (row, col) => {
    switch (selectedTool) {
      case "brush":
      case "eraser":
        drawAtPosition(row, col);
        break;
      case "line":
        handleLineTool(row, col);
        break;
      case "rectangle":
        handleRectangleTool(row, col);
        break;
      case "fill":
        floodFill(row, col);
        break;
      case "eyedropper":
        pickColor(row, col);
        break;
      case "select":
        handleSelection(row, col);
        break;
    }
  };

  const drawAtPosition = (row, col) => {
    if (row < 0 || row >= canvasHeight || col < 0 || col >= canvasWidth) return;
    drawOpsRef.current += 1;
    // Initialize working canvas if needed
    if (!nextCanvasRef.current) {
      nextCanvasRef.current = canvas.map((r) => [...r]);
    }
    const target = nextCanvasRef.current;
    const char = selectedTool === "eraser" ? " " : selectedChar;

    // Draw with brush size (circular brush)
    for (let r = row - Math.floor(brushSize / 2); r <= row + Math.floor(brushSize / 2); r++) {
      for (let c = col - Math.floor(brushSize / 2); c <= col + Math.floor(brushSize / 2); c++) {
        if (r >= 0 && r < canvasHeight && c >= 0 && c < canvasWidth) {
          const distance = Math.sqrt((r - row) ** 2 + (c - col) ** 2);
          if (distance <= brushSize / 2) {
            target[r][c] = char;
          }
        }
      }
    }

    // Commit at most once per frame
    scheduleCommit();
  };

  // Enhanced tool functions
  const handleLineTool = (row, col) => {
    if (!lineStart) {
      setLineStart({ row, col });
    } else {
      drawLine(lineStart.row, lineStart.col, row, col);
      setLineStart(null);
    }
  };

  const drawLine = (startRow, startCol, endRow, endCol) => {
    const newCanvas = [...canvas.map((row) => [...row])];
    
    const dx = Math.abs(endCol - startCol);
    const dy = Math.abs(endRow - startRow);
    const sx = startCol < endCol ? 1 : -1;
    const sy = startRow < endRow ? 1 : -1;
    let err = dx - dy;

    let currentRow = startRow;
    let currentCol = startCol;

    while (true) {
      if (currentRow >= 0 && currentRow < canvasHeight && currentCol >= 0 && currentCol < canvasWidth) {
        newCanvas[currentRow][currentCol] = selectedChar;
      }

      if (currentRow === endRow && currentCol === endCol) break;

      const e2 = 2 * err;
      if (e2 > -dy) {
        err -= dy;
        currentCol += sx;
      }
      if (e2 < dx) {
        err += dx;
        currentRow += sy;
      }
    }

    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const handleRectangleTool = (row, col) => {
    if (!rectStart) {
      setRectStart({ row, col });
    } else {
      drawRectangle(rectStart.row, rectStart.col, row, col);
      setRectStart(null);
    }
  };

  const drawRectangle = (startRow, startCol, endRow, endCol) => {
    const newCanvas = [...canvas.map((row) => [...row])];
    
    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);

    // Draw rectangle outline or filled
    for (let r = minRow; r <= maxRow; r++) {
      for (let c = minCol; c <= maxCol; c++) {
        if (r >= 0 && r < canvasHeight && c >= 0 && c < canvasWidth) {
          // If shift is pressed, draw filled rectangle
          if (isShiftPressed || r === minRow || r === maxRow || c === minCol || c === maxCol) {
            newCanvas[r][c] = selectedChar;
          }
        }
      }
    }

    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const floodFill = (startRow, startCol) => {
    if (startRow < 0 || startRow >= canvasHeight || startCol < 0 || startCol >= canvasWidth) return;
    
    const newCanvas = [...canvas.map((row) => [...row])];
    const targetChar = newCanvas[startRow][startCol];
    const replacementChar = selectedChar;
    
    if (targetChar === replacementChar) return;

    const stack = [[startRow, startCol]];
    
    while (stack.length > 0) {
      const [row, col] = stack.pop();
      
      if (row < 0 || row >= canvasHeight || col < 0 || col >= canvasWidth) continue;
      if (newCanvas[row][col] !== targetChar) continue;
      
      newCanvas[row][col] = replacementChar;
      
      // Add adjacent cells to stack
      stack.push([row + 1, col], [row - 1, col], [row, col + 1], [row, col - 1]);
    }

    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const pickColor = (row, col) => {
    if (row >= 0 && row < canvasHeight && col >= 0 && col < canvasWidth) {
      const pickedChar = canvas[row][col];
      setSelectedChar(pickedChar === " " ? "█" : pickedChar);
      setSelectedTool("brush"); // Switch back to brush after picking
    }
  };

  const handleSelection = (row, col) => {
    if (!selection) {
      setSelection({ startRow: row, startCol: col, endRow: row, endCol: col });
    } else {
      setSelection({ ...selection, endRow: row, endCol: col });
    }
  };

  const copySelection = () => {
    if (!selection) return;
    
    const minRow = Math.min(selection.startRow, selection.endRow);
    const maxRow = Math.max(selection.startRow, selection.endRow);
    const minCol = Math.min(selection.startCol, selection.endCol);
    const maxCol = Math.max(selection.startCol, selection.endCol);
    
    const copied = [];
    for (let r = minRow; r <= maxRow; r++) {
      const row = [];
      for (let c = minCol; c <= maxCol; c++) {
        row.push(canvas[r] && canvas[r][c] ? canvas[r][c] : " ");
      }
      copied.push(row);
    }
    
    setClipboard(copied);
  };

  const pasteSelection = (row, col) => {
    if (!clipboard) return;
    
    const newCanvas = [...canvas.map((row) => [...row])];
    
    for (let r = 0; r < clipboard.length; r++) {
      for (let c = 0; c < clipboard[r].length; c++) {
        const targetRow = row + r;
        const targetCol = col + c;
        if (targetRow >= 0 && targetRow < canvasHeight && targetCol >= 0 && targetCol < canvasWidth) {
          if (clipboard[r][c] !== " ") {
            newCanvas[targetRow][targetCol] = clipboard[r][c];
          }
        }
      }
    }
    
    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const handleMouseDown = (row, col, e) => {
    e.preventDefault();
    
    if (e.button === 2) { // Right click
      handleRightClick(row, col, e);
      return;
    }
    
    setIsDrawing(true);
    handleCanvasClick(row, col);
  };

  const handleRightClick = (row, col, e) => {
    e.preventDefault();
    
    // Quick actions on right click
    if (selectedTool === "brush") {
      // Right click to pick color
      pickColor(row, col);
    } else if (clipboard) {
      // Right click to paste
      pasteSelection(row, col);
    }
  };

  const handleMouseEnter = (row, col) => {
    setCursorPos({ x: col, y: row });
    if (isDrawing && (selectedTool === "brush" || selectedTool === "eraser")) {
      drawAtPosition(row, col);
    }
  };

  // Responsive viewport detection and canvas sizing
  useEffect(() => {
    const checkViewport = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setViewportSize({ width, height });
      setIsMobile(width < 768);

      // Auto-hide sidebar on small screens
      if (width < 1024) {
        setShowLeftSidebar(false);
      } else {
        setShowLeftSidebar(true);
      }

      // Calculate responsive canvas cell size accounting for docked AI panel
      const leftSidebarWidth = showLeftSidebar && width >= 1024 ? 320 : 0;
      const aiPanelDockWidth = (aiDockMode !== 'floating' && showAI && width >= 768) ? aiPanelWidth : 0;
      const availableWidth = width - leftSidebarWidth - aiPanelDockWidth - 100;
      const availableHeight = height - 200; // Account for toolbar and padding

      const maxCellWidth = Math.max(4, Math.min(10, availableWidth / canvasWidth));
      const maxCellHeight = Math.max(8, Math.min(20, availableHeight / canvasHeight));

      // Maintain aspect ratio
      const cellWidth = Math.min(maxCellWidth, maxCellHeight * 0.5);
      const cellHeight = cellWidth * 2;

      setCellSize({ width: cellWidth, height: cellHeight });

      // Calculate scale to fit content
      const scaleX = availableWidth / (canvasWidth * cellWidth);
      const scaleY = availableHeight / (canvasHeight * cellHeight);
      const optimalScale = Math.min(1, Math.min(scaleX, scaleY));

      setCanvasScale(optimalScale);
    };

    checkViewport();
    window.addEventListener('resize', checkViewport);

    return () => window.removeEventListener('resize', checkViewport);
  }, [canvasWidth, canvasHeight, showLeftSidebar, aiDockMode, showAI, aiPanelWidth]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      setIsShiftPressed(e.shiftKey);
      
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              redo();
            } else {
              undo();
            }
            break;
          case 'c':
            if (selection) {
              e.preventDefault();
              copySelection();
            }
            break;
          case 'v':
            if (clipboard) {
              e.preventDefault();
              // Paste at cursor position or center
              pasteSelection(Math.floor(canvasHeight / 2), Math.floor(canvasWidth / 2));
            }
            break;
          case '\\':
            e.preventDefault();
            setShowLeftSidebar(!showLeftSidebar);
            break;
          case '[':
            e.preventDefault();
            setSidebarCompact(!sidebarCompact);
            break;
        }
      }
      
      // Tool shortcuts
      switch (e.key.toLowerCase()) {
        case 'b':
          setSelectedTool("brush");
          break;
        case 'e':
          setSelectedTool("eraser");
          break;
        case 'l':
          setSelectedTool("line");
          break;
        case 'r':
          setSelectedTool("rectangle");
          break;
        case 'f':
          setSelectedTool("fill");
          break;
        case 'i':
          setSelectedTool("eyedropper");
          break;
        case 's':
          if (!e.ctrlKey && !e.metaKey) {
            setSelectedTool("select");
          }
          break;
        case 'escape':
          setSelection(null);
          setLineStart(null);
          setRectStart(null);
          break;
      }
    };

    const handleKeyUp = (e) => {
      setIsShiftPressed(e.shiftKey);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [selection, clipboard, undo, redo, copySelection, pasteSelection, showLeftSidebar, sidebarCompact]);

  const handleMouseUp = (e) => {
    e.preventDefault();
    // Ensure the latest changes are rendered before recording history
    flushPending();
    if (nextCanvasRef.current) {
      addToHistory(nextCanvasRef.current);
      nextCanvasRef.current = null;
    } else {
      addToHistory(canvas);
    }
    setIsDrawing(false);
  };

  const handleMouseLeave = (e) => {
    e.preventDefault();
    flushPending();
    if (isDrawing) {
      if (nextCanvasRef.current) {
        addToHistory(nextCanvasRef.current);
        nextCanvasRef.current = null;
      } else {
        addToHistory(canvas);
      }
    }
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const newCanvas = Array(canvasHeight)
      .fill(null)
      .map(() => Array(canvasWidth).fill(" "));
    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const exportAsText = () => {
    const text = canvas.map((row) => row.join("")).join("\n");
    const blob = new Blob([text], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "ascii-art.txt";
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyToClipboard = () => {
    const text = canvas.map((row) => row.join("")).join("\n");
    navigator.clipboard.writeText(text);
  };

  // Removed browser usage of @mastra/core; using server API instead

  const sendToAI = useCallback(async () => {
    if (!aiInput.trim()) return;

    setAiLoading(true);
    const userMessage = { role: "user", content: aiInput };
    setAiMessages((prev) => [...prev, userMessage]);
    const currentMessage = aiInput;
    setAiInput("");

    try {
      // Slash commands: /fetch <url>, /crawl <url> [maxChars]
      if (currentMessage.startsWith('/fetch ')) {
        const url = currentMessage.slice('/fetch '.length).trim();
        const res = await fetch('/api/tools/fetch', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ url }),
        });
        const data = await res.json();
        setAiMessages((prev) => [
          ...prev,
          { role: 'assistant', content: data.error ? `Fetch error: ${data.error}` : `Fetched ${data.status} ${data.contentType}${data.truncated ? ' (truncated)' : ''}\n\n${data.bodyText ?? (data.bodyJson ? JSON.stringify(data.bodyJson, null, 2) : '[binary/non-text]')}` },
        ]);
        return;
      }
      if (currentMessage.startsWith('/crawl ')) {
        const parts = currentMessage.slice('/crawl '.length).trim().split(/\s+/);
        const url = parts[0];
        const maxChars = parts[1] ? parseInt(parts[1]) || undefined : undefined;
        const res = await fetch('/api/tools/crawl', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ url, maxChars }),
        });
        const data = await res.json();
        setAiMessages((prev) => [
          ...prev,
          { role: 'assistant', content: data.error ? `Crawl error: ${data.error}` : `Crawled ${data.status}${data.truncated ? ' (truncated)' : ''} from ${data.url}\n\n${data.text || '[no text extracted]'}` },
        ]);
        return;
      }
      if (currentMessage.startsWith('/tool ')) {
        const jsonPart = currentMessage.slice('/tool '.length).trim();
        let payload;
        try { payload = JSON.parse(jsonPart); } catch {
          setAiMessages((prev) => [...prev, { role: 'assistant', content: 'Invalid /tool JSON. Example: /tool {"toolId":"fetch","args":{"url":"https://example.com"}}' }]);
          return;
        }
        const res = await fetch('/api/tools/run', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        const data = await res.json();
        setAiMessages((prev) => [
          ...prev,
          { role: 'assistant', content: data.error ? `Tool error: ${data.error}` : `Tool result:\n${JSON.stringify(data, null, 2)}` },
        ]);
        return;
      }

      const res = await fetch('/api/ai-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: currentMessage,
          canvasSize: { width: canvasWidth, height: canvasHeight },
          currentCanvas: canvas,
        }),
      });
      const data = await res.json();

      const aiMessage = { role: "assistant", content: data.message || "" };
      setAiMessages((prev) => [...prev, aiMessage]);

      if (data.asciiArt) {
        const applyMessage = {
          role: "assistant",
          content: "Apply to canvas?",
          asciiArt: data.asciiArt,
          action: "apply",
        };
        setAiMessages((prev) => [...prev, applyMessage]);
      }
    } catch (error) {
      setAiMessages((prev) => [...prev, { role: "assistant", content: "Sorry, please try again." }]);
    } finally {
      setAiLoading(false);
    }
  }, [aiInput, canvasWidth, canvasHeight, canvas]);

  // Chat thread management functions
  const createNewThread = useCallback(() => {
    const newThread = {
      id: nextThreadId.toString(),
      title: `Chat ${nextThreadId}`,
      createdAt: new Date().toISOString(),
      messages: [
        {
          role: "assistant",
          content: "Hi! I can help you create ASCII art. Try asking me to generate art from text, or ask for tips!",
        },
      ],
    };

    setChatThreads(prev => ({
      ...prev,
      [newThread.id]: newThread,
    }));
    setCurrentThreadId(newThread.id);
    setNextThreadId(prev => prev + 1);
  }, [nextThreadId]);

  const switchToThread = useCallback((threadId) => {
    if (chatThreads[threadId]) {
      setCurrentThreadId(threadId);
    }
  }, [chatThreads]);

  const clearCurrentThread = useCallback(() => {
    if (!currentThreadId) return;

    setChatThreads(prev => {
      const newThreads = { ...prev };
      if (newThreads[currentThreadId]) {
        newThreads[currentThreadId] = {
          ...newThreads[currentThreadId],
          messages: [
            {
              role: "assistant",
              content: "Hi! I can help you create ASCII art. Try asking me to generate art from text, or ask for tips!",
            },
          ],
          updatedAt: new Date().toISOString(),
        };
      }
      return newThreads;
    });
  }, [currentThreadId]);

  const deleteCurrentThread = useCallback(() => {
    if (!currentThreadId) return;

    setChatThreads(prev => {
      const newThreads = { ...prev };
      delete newThreads[currentThreadId];

      // If this was the last thread, create a new one
      const remainingThreads = Object.keys(newThreads);
      if (remainingThreads.length === 0) {
        const newThread = {
          id: nextThreadId.toString(),
          title: `Chat ${nextThreadId}`,
          createdAt: new Date().toISOString(),
          messages: [
            {
              role: "assistant",
              content: "Hi! I can help you create ASCII art. Try asking me to generate art from text, or ask for tips!",
            },
          ],
        };
        newThreads[newThread.id] = newThread;
        setCurrentThreadId(newThread.id);
        setNextThreadId(prev => prev + 1);
      } else {
        // Switch to the most recent remaining thread
        const sortedThreads = remainingThreads.sort((a, b) =>
          new Date(newThreads[b].updatedAt || newThreads[b].createdAt).getTime() -
          new Date(newThreads[a].updatedAt || newThreads[a].createdAt).getTime()
        );
        setCurrentThreadId(sortedThreads[0]);
      }

      return newThreads;
    });
  }, [currentThreadId, nextThreadId]);

  const updateThreadTitle = useCallback((threadId, newTitle) => {
    setChatThreads(prev => {
      const newThreads = { ...prev };
      if (newThreads[threadId]) {
        newThreads[threadId] = {
          ...newThreads[threadId],
          title: newTitle,
          updatedAt: new Date().toISOString(),
        };
      }
      return newThreads;
    });
  }, []);

  // AI Panel docking functions
  const dockAIPanel = useCallback((mode) => {
    setAiDockMode(mode);
    if (mode !== 'floating') {
      // Ensure AI panel is visible when docking
      setShowAI(true);
    }
  }, []);

  const handleResizeStart = useCallback(() => {
    setIsResizing(true);
  }, []);

  const handleResize = useCallback((newWidth) => {
    const minWidth = 300;
    const maxWidth = Math.min(800, viewportSize.width * 0.4);
    const clampedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
    setAiPanelWidth(clampedWidth);
  }, [viewportSize.width]);

  const handleResizeEnd = useCallback(() => {
    setIsResizing(false);
  }, []);

  // Handle mouse events for resizing
  useEffect(() => {
    if (!isResizing) return;

    const handleMouseMove = (e) => {
      if (aiDockMode === 'left') {
        handleResize(e.clientX);
      } else if (aiDockMode === 'right') {
        handleResize(viewportSize.width - e.clientX);
      }
    };

    const handleMouseUp = () => {
      handleResizeEnd();
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, aiDockMode, handleResize, handleResizeEnd, viewportSize.width]);

  const applyAIArt = useCallback(
    (asciiArt) => {
      const sanitized = String(asciiArt)
        .replace(/\r\n/g, "\n")
        .replace(/\t/g, "  ")
        .replace(/^\s*```[a-zA-Z]*\s*|\s*```\s*$/g, "");

      const rawLines = sanitized.split("\n");
      // Trim leading/trailing empty lines
      while (rawLines.length && rawLines[0].trim() === "") rawLines.shift();
      while (rawLines.length && rawLines[rawLines.length - 1].trim() === "") rawLines.pop();

      // Wrap long lines to the canvas width
      const wrappedLines = [];
      for (const line of rawLines) {
        if (line.length <= canvasWidth) {
          wrappedLines.push(line);
        } else {
          for (let i = 0; i < line.length; i += canvasWidth) {
            wrappedLines.push(line.slice(i, i + canvasWidth));
          }
        }
      }

      // Clip to canvas height
      const contentLines = wrappedLines.slice(0, canvasHeight);

      const newCanvas = Array(canvasHeight)
        .fill(null)
        .map(() => Array(canvasWidth).fill(" "));

      // Vertically center content
      const verticalPadding = Math.max(0, Math.floor((canvasHeight - contentLines.length) / 2));

      // Place each line centered horizontally
      for (let r = 0; r < contentLines.length; r++) {
        const rowContent = contentLines[r];
        const effectiveLength = Math.min(canvasWidth, rowContent.length);
        const horizontalPadding = Math.max(0, Math.floor((canvasWidth - effectiveLength) / 2));
        for (let c = 0; c < effectiveLength; c++) {
          newCanvas[verticalPadding + r][horizontalPadding + c] = rowContent[c];
        }
      }

      setCanvas(newCanvas);
      addToHistory(newCanvas);
      setAiMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "Applied to canvas! Feel free to edit further or ask for more help.",
        },
      ]);
    },
    [canvasHeight, canvasWidth, addToHistory],
  );

  const tools = [
    { id: "brush", icon: Edit3, label: "Brush", shortcut: "B" },
    { id: "eraser", icon: Eraser, label: "Eraser", shortcut: "E" },
    { id: "line", icon: Minus, label: "Line", shortcut: "L" },
    { id: "rectangle", icon: Square, label: "Rectangle", shortcut: "R" },
    { id: "fill", icon: () => <div className="w-4 h-4 border border-current rounded-sm bg-current"></div>, label: "Fill", shortcut: "F" },
    { id: "eyedropper", icon: () => <div className="w-4 h-4 border-2 border-current rounded-full relative"><div className="absolute inset-1 bg-current rounded-full"></div></div>, label: "Eyedropper", shortcut: "I" },
    { id: "select", icon: () => <div className="w-4 h-4 border-2 border-current border-dashed"></div>, label: "Select", shortcut: "S" },
    { id: "text", icon: Type, label: "Text", shortcut: "T" },
  ];

  return (
    <div className="min-h-[100dvh] bg-[#0a0a0a] text-[#00ff00] font-mono relative flex flex-col overflow-hidden">
      {/* Top Toolbar */}
      <TopToolbar
        showLeftSidebar={showLeftSidebar}
        toggleLeftSidebar={() => setShowLeftSidebar(!showLeftSidebar)}
        clearCanvas={clearCanvas}
        undo={undo}
        redo={redo}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
        exportAsText={exportAsText}
        copyToClipboard={copyToClipboard}
        selection={selection}
        copySelection={copySelection}
        clipboard={clipboard}
        pasteSelectionCenter={() => pasteSelection(Math.floor(canvasHeight / 2), Math.floor(canvasWidth / 2))}
        showGrid={showGrid}
        setShowGrid={setShowGrid}
        zoom={zoom}
        setZoom={setZoom}
        canvasScale={canvasScale}
        showAI={showAI}
        setShowAI={setShowAI}
        tools={tools}
        selectedTool={selectedTool}
        setSelectedTool={setSelectedTool}
      />

      <div className="flex flex-1 min-h-0 relative max-w-full">
        {/* Left Docked AI Panel */}
        {showAI && aiDockMode === 'left' && (
          <>
            <div
              className="flex-shrink-0 border-r border-[#333333] bg-[#1a1a1a] flex flex-col overflow-hidden h-full"
              style={{ width: `${aiPanelWidth}px` }}
            >
              <AIPanel
                isMobile={isMobile}
                aiProvider={aiProvider}
                setAiProvider={setAiProvider}
                aiMessages={aiMessages}
                aiLoading={aiLoading}
                aiInput={aiInput}
                setAiInput={setAiInput}
                sendToAI={sendToAI}
                applyAIArt={applyAIArt}
                close={() => setShowAI(false)}
                chatThreads={chatThreads}
                currentThreadId={currentThreadId}
                createNewThread={createNewThread}
                switchToThread={switchToThread}
                clearCurrentThread={clearCurrentThread}
                deleteCurrentThread={deleteCurrentThread}
                updateThreadTitle={updateThreadTitle}
                dockMode={aiDockMode}
                dockAIPanel={dockAIPanel}
                isDocked={true}
              />
            </div>
            {/* Resizable divider */}
            <div
              className="w-1 bg-[#333333] hover:bg-[#555555] cursor-col-resize flex-shrink-0 transition-colors"
              onMouseDown={handleResizeStart}
            />
          </>
        )}

        {/* Left Panel - Tools & Character Palette */}
        <div className={`${
          showLeftSidebar ? 'translate-x-0' : '-translate-x-full'
        } fixed lg:relative lg:translate-x-0 z-30 ${
          sidebarCompact ? 'w-64 lg:w-64 xl:w-72' : 'w-72 sm:w-80 lg:w-80 xl:w-96 2xl:w-80'
        } border-r border-[#333333] bg-[#1a1a1a] flex flex-col overflow-hidden flex-shrink-0 h-full max-h-[100dvh] lg:max-h-full transition-all duration-300 ease-in-out`}>
          {/* Fixed Header */}
          <div className="p-3 pb-0 flex-shrink-0">
            <div className="flex items-center justify-between mb-3 lg:hidden">
              <h3 className="text-sm font-bold text-[#00ff00]">TOOLS & PALETTE</h3>
              <button onClick={() => setShowLeftSidebar(false)} className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors">
                <X size={20} />
              </button>
            </div>
            {/* Sidebar Controls - Desktop Only */}
            <div className="hidden lg:flex items-center justify-between mb-2">
              <div className="text-xs text-[#666666]">Sections</div>
              <div className="flex space-x-1">
                <button
                  onClick={() => setSidebarCompact(!sidebarCompact)}
                  className={`px-2 py-1 text-xs rounded transition-colors ${
                    sidebarCompact
                      ? 'text-[#00ff00] bg-[#2a2a2a]'
                      : 'text-[#666666] hover:text-[#cccccc] hover:bg-[#2a2a2a]'
                  }`}
                  title={sidebarCompact ? "Expand Sidebar" : "Compact Sidebar"}
                >
                  {sidebarCompact ? 'Wide' : 'Compact'}
                </button>
                <button
                  onClick={() => setCollapsedSections({ tools: true, palette: true, shortcuts: true, canvasSettings: true })}
                  className="px-2 py-1 text-xs text-[#666666] hover:text-[#cccccc] hover:bg-[#2a2a2a] rounded transition-colors"
                  title="Collapse All Sections"
                >
                  Collapse All
                </button>
                <button
                  onClick={() => setCollapsedSections({ tools: false, palette: false, shortcuts: false, canvasSettings: false })}
                  className="px-2 py-1 text-xs text-[#666666] hover:text-[#cccccc] hover:bg-[#2a2a2a] rounded transition-colors"
                  title="Expand All Sections"
                >
                  Expand All
                </button>
              </div>
            </div>
          </div>

          {/* Fixed Priority Content */}
          <div className="px-3 flex-shrink-0">
            <div className="mb-4">
            <button onClick={() => setCollapsedSections(prev => ({ ...prev, tools: !prev.tools }))} className="hidden lg:flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hover:text-[#00cc00] transition-colors">
              <span>TOOLS</span>
              {collapsedSections.tools ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
            </button>
            <h3 className="text-xs font-bold text-[#00ff00] mb-2 lg:hidden">TOOLS</h3>
            {!collapsedSections.tools && (
            <div className="grid grid-cols-2 xl:grid-cols-2 gap-1.5">
              {tools.map((tool) => {
                const Icon = tool.icon;
                return (
                  <button
                    key={tool.id}
                    onClick={() => setSelectedTool(tool.id)}
                    title={`${tool.label} (${tool.shortcut})`}
                    className={`p-2 border rounded text-xs flex flex-col items-center space-y-0.5 transition-colors relative ${
                      selectedTool === tool.id
                        ? "bg-[#00ff00] text-[#000000] border-[#00ff00]"
                        : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a] active:bg-[#4a4a4a]"
                    }`}
                  >
                    {typeof Icon === 'function' ? <Icon /> : <Icon size={16} />}
                    <span>{tool.label}</span>
                    <span className="absolute top-1 right-1 text-[10px] opacity-60">
                      {tool.shortcut}
                    </span>
                  </button>
                );
              })}
            </div>
            )}
            
            {/* Tool Options */}
            {selectedTool === "brush" && (
              <div className="mt-3 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-1">Brush Size</div>
                <input type="range" min="1" max="10" value={brushSize} onChange={(e) => setBrushSize(parseInt(e.target.value))} className="w-full" />
                <div className="text-xs text-[#666666] mt-1">Size: {brushSize}</div>
              </div>
            )}
            
            {(lineStart || rectStart) && (
              <div className="mt-2 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-1">{lineStart ? "Click to finish line" : "Click to finish rectangle"}</div>
                <div className="text-xs text-[#666666] mb-2">Hold Shift for {rectStart ? "filled rectangle" : "constrained line"}</div>
                <button onClick={() => { setLineStart(null); setRectStart(null); }} className="px-2 py-1 bg-[#444444] hover:bg-[#555555] text-xs rounded">
                  Cancel (Esc)
                </button>
              </div>
            )}
            
            {selection && (
              <div className="mt-2 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-2">Selection Active</div>
                <div className="flex gap-1">
                  <button onClick={copySelection} className="px-2 py-1 bg-[#444444] hover:bg-[#555555] text-xs rounded">Copy</button>
                  <button onClick={() => setSelection(null)} className="px-2 py-1 bg-[#444444] hover:bg-[#555555] text-xs rounded">Clear</button>
                </div>
              </div>
            )}
            
            {clipboard && (
              <div className="mt-2 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-1">Clipboard: {clipboard.length}×{clipboard[0]?.length || 0}</div>
                <button onClick={() => pasteSelection(Math.floor(canvasHeight / 2), Math.floor(canvasWidth / 2))} className="px-2 py-1 bg-[#00ff00] text-[#000000] hover:bg-[#00cc00] text-xs rounded">Paste</button>
              </div>
            )}
          </div>

          {/* Quick Colors */}
          <div className="mb-4">
            <button onClick={() => setCollapsedSections(prev => ({ ...prev, palette: !prev.palette }))} className="flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hover:text-[#00cc00] transition-colors">
              <span>QUICK PALETTE</span>
              {collapsedSections.palette ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
            </button>
            {!collapsedSections.palette && (
            <div className="grid grid-cols-8 gap-1 mb-3">
              {["█", "▓", "▒", "░", "■", "□", "●", "○"].map((char, index) => (
                <button
                  key={`quick-${index}`}
                  onClick={() => setSelectedChar(char)}
                  className={`w-7 h-7 border rounded text-center flex items-center justify-center transition-colors ${
                    selectedChar === char
                      ? "bg-[#00ff00] text-[#000000] border-[#00ff00]"
                      : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a] active:bg-[#4a4a4a]"
                  }`}
                >
                  {char}
                </button>
              ))}
            </div>
            )}
          </div>


            {/* Canvas Settings - Collapsible */}
            <div className="mb-4">
              <button
                onClick={() => setCollapsedSections(prev => ({ ...prev, canvasSettings: !prev.canvasSettings }))}
                className="flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hover:text-[#00cc00] transition-colors"
              >
                <span className="flex items-center">
                  <span className="mr-2">🎨</span>CANVAS SETTINGS
                </span>
                {collapsedSections.canvasSettings ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
              </button>
              {!collapsedSections.canvasSettings && (
              <div className="bg-[#1a1a1a] border border-[#00ff00]/30 rounded-lg p-3">
                <div className="space-y-3">
                <div>
                  <label className="text-xs text-[#888888] mb-1 block">Width</label>
                  <input type="number" value={canvasWidth} onChange={(e) => setCanvasWidth(Math.max(1, Math.min(200, parseInt(e.target.value) || 80)))} className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs" min="1" max="200" />
                </div>
                <div>
                  <label className="text-xs text-[#888888] mb-1 block">Height</label>
                  <input type="number" value={canvasHeight} onChange={(e) => setCanvasHeight(Math.max(1, Math.min(100, parseInt(e.target.value) || 24)))} className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs" min="1" max="100" />
                </div>
                
                {/* Additional Canvas Options */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs text-[#888888] mb-1 block">Preset</label>
                    <select className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs" onChange={(e) => {
                      const [w, h] = e.target.value.split('x').map(Number);
                      if (w && h) { setCanvasWidth(w); setCanvasHeight(h); }
                    }}>
                      <option value="">Custom</option>
                      <option value="80x24">Terminal (80×24)</option>
                      <option value="120x30">Large (120×30)</option>
                      <option value="40x20">Small (40×20)</option>
                      <option value="100x50">Banner (100×50)</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-xs text-[#888888] mb-1 block">Aspect</label>
                    <button onClick={() => {
                      const aspectRatio = canvasWidth / canvasHeight;
                      const newHeight = Math.round(canvasWidth / aspectRatio);
                      setCanvasHeight(Math.max(1, Math.min(100, newHeight)));
                    }} className="w-full px-2 py-1 bg-[#444444] hover:bg-[#555555] border border-[#666666] rounded text-[#cccccc] text-xs">
                      Lock Ratio
                    </button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-2 border-t border-[#333333]">
                  <span className="text-xs text-[#888888]">Size: {canvasWidth}×{canvasHeight}</span>
                  <span className="text-xs text-[#666666]">{canvasWidth * canvasHeight} cells</span>
                </div>
                </div>
              </div>
              )}
            </div>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 min-h-0 overflow-y-auto overflow-x-hidden px-3 pb-4">
            {/* Character Sets */}
            <div className="mb-4">
              <h3 className="text-xs font-bold text-[#00ff00] mb-2">CHARACTER SETS</h3>
              {Object.entries(charSets).map(([setName, chars]) => (
                <div key={setName} className="mb-3">
                  <button onClick={() => setExpandedCharSets(prev => ({ ...prev, [setName]: !prev[setName] }))} className="flex items-center justify-between w-full text-xs text-[#888888] mb-1.5 uppercase hover:text-[#cccccc] transition-colors">
                    <span>{setName}</span>
                    {expandedCharSets[setName] ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
                  </button>
                  {(expandedCharSets[setName] !== false) && (
                    <div className="grid grid-cols-8 gap-1">
                      {chars.map((char, index) => (
                        <button key={`${setName}-${index}`} onClick={() => setSelectedChar(char)} className={`w-6 h-6 border rounded text-center flex items-center justify-center transition-colors text-xs ${selectedChar === char ? "bg-[#00ff00] text-[#000000] border-[#00ff00]" : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a] active:bg-[#4a4a4a]"}`}>
                          {char}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Keyboard Shortcuts */}
            <div>
            <button onClick={() => setCollapsedSections(prev => ({ ...prev, shortcuts: !prev.shortcuts }))} className="flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hover:text-[#00cc00] transition-colors">
              <span>SHORTCUTS</span>
              {collapsedSections.shortcuts ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
            </button>
            {!collapsedSections.shortcuts && (
            <div className="space-y-0.5 text-xs text-[#888888]">
              <div className="flex justify-between">
                <span>Brush</span>
                <span className="text-[#666666]">B</span>
              </div>
              <div className="flex justify-between">
                <span>Eraser</span>
                <span className="text-[#666666]">E</span>
              </div>
              <div className="flex justify-between">
                <span>Line</span>
                <span className="text-[#666666]">L</span>
              </div>
              <div className="flex justify-between">
                <span>Rectangle</span>
                <span className="text-[#666666]">R</span>
              </div>
              <div className="flex justify-between">
                <span>Fill</span>
                <span className="text-[#666666]">F</span>
              </div>
              <div className="flex justify-between">
                <span>Eyedropper</span>
                <span className="text-[#666666]">I</span>
              </div>
              <div className="flex justify-between">
                <span>Select</span>
                <span className="text-[#666666]">S</span>
              </div>
              <div className="border-t border-[#333333] my-1"></div>
              <div className="flex justify-between">
                <span>Undo</span>
                <span className="text-[#666666]">Ctrl+Z</span>
              </div>
              <div className="flex justify-between">
                <span>Redo</span>
                <span className="text-[#666666]">Ctrl+Shift+Z</span>
              </div>
              <div className="flex justify-between">
                <span>Copy</span>
                <span className="text-[#666666]">Ctrl+C</span>
              </div>
              <div className="flex justify-between">
                <span>Paste</span>
                <span className="text-[#666666]">Ctrl+V</span>
              </div>
              <div className="flex justify-between">
                <span>Cancel</span>
                <span className="text-[#666666]">Esc</span>
              </div>
              <div className="border-t border-[#333333] my-1"></div>
              <div className="flex justify-between">
                <span>Toggle Sidebar</span>
                <span className="text-[#666666]">Ctrl+\</span>
              </div>
              <div className="flex justify-between">
                <span>Compact Mode</span>
                <span className="text-[#666666]">Ctrl+[</span>
              </div>
              <div className="border-t border-[#333333] my-1"></div>
              <div className="text-[#666666] text-[10px]">
                Right-click: Pick color or paste
              </div>
              <div className="text-[#666666] text-[10px]">Shift+Rectangle: Fill mode</div>
            </div>
            )}
          </div>
          </div>
        </div>

        {/* Overlay for mobile sidebar */}
        {showLeftSidebar && (
          <div 
            className="fixed inset-0 bg-black/50 z-20 lg:hidden"
            onClick={() => setShowLeftSidebar(false)}
          />
        )}

        {/* Mobile Floating Toolbar */}
        {!showLeftSidebar && (
          <div className="fixed bottom-4 left-2 right-2 z-30 lg:hidden">
            <div className="bg-[#1a1a1a]/95 backdrop-blur-sm border border-[#333333] rounded-lg p-2 shadow-lg max-w-full">
              <div className="flex items-center justify-center space-x-1 overflow-x-auto scrollbar-hide">
                {tools.slice(0, 6).map((tool) => {
                  const Icon = tool.icon;
                  return (
                    <button
                      key={tool.id}
                      onClick={() => setSelectedTool(tool.id)}
                      className={`p-2 border rounded transition-colors flex-shrink-0 min-w-[40px] ${
                        selectedTool === tool.id
                          ? "bg-[#00ff00] text-[#000000] border-[#00ff00]"
                          : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a]"
                      }`}
                      title={tool.label}
                    >
                      {typeof Icon === 'function' ? <Icon /> : <Icon size={14} />}
                    </button>
                  );
                })}
                <div className="w-px h-6 bg-[#333333] mx-1"></div>
                <button
                  onClick={() => setShowLeftSidebar(true)}
                  className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] border border-[#444444] rounded text-[#cccccc] transition-colors flex-shrink-0 min-w-[40px]"
                  title="More Tools"
                >
                  <MoreHorizontal size={14} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Center Panel - Full Screen Canvas */}
        <div className="flex-1 bg-[#0a0a0a] flex flex-col min-w-0 relative overflow-hidden max-w-full">
          {/* Floating Status Bar */}
          <div className="absolute top-2 left-2 sm:top-4 sm:left-4 z-10 bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#333333] rounded-lg px-2 sm:px-3 py-1 sm:py-2 flex items-center space-x-2 sm:space-x-4 text-xs text-[#888888] max-w-[calc(100%-1rem)] sm:max-w-none overflow-hidden">
            <span className="text-[#00ff00] font-bold truncate">Tool: {tools.find(t => t.id === selectedTool)?.label}</span>
            <span className="hidden sm:inline">Pos: {cursorPos.x}, {cursorPos.y}</span>
            <span className="hidden md:inline">Size: {canvasWidth}×{canvasHeight}</span>
            {selectedTool === "brush" && <span className="hidden lg:inline">Brush: {brushSize}px</span>}
          </div>

          {/* Full Screen Canvas Area */}
          <div className="flex-1 w-full h-full overflow-auto flex items-center justify-center p-2 sm:p-4 relative">
            {/* Scroll hint for mobile */}
            {(zoom * canvasScale > 1 || canvasWidth * cellSize.width > viewportSize.width - 200) && (
              <div className="absolute top-2 right-2 z-10 bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#333333] rounded px-2 py-1 text-xs text-[#888888] sm:hidden">
                Scroll to pan
              </div>
            )}
            <div className="flex items-center justify-center max-w-full max-h-full">
              <CanvasGrid
                ref={canvasRef}
                canvas={canvas}
                canvasWidth={canvasWidth}
                canvasHeight={canvasHeight}
                cellSize={cellSize}
                canvasScale={canvasScale}
                zoom={zoom}
                selection={selection}
                cursorPos={cursorPos}
                showGrid={showGrid}
                onCellMouseDown={handleMouseDown}
                onCellMouseEnter={handleMouseEnter}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
              />
              </div>
            </div>
        </div>

        {/* Right Docked AI Panel */}
        {showAI && aiDockMode === 'right' && (
          <>
            {/* Resizable divider */}
            <div
              className="w-1 bg-[#333333] hover:bg-[#555555] cursor-col-resize flex-shrink-0 transition-colors"
              onMouseDown={handleResizeStart}
            />
            <div
              className="flex-shrink-0 border-l border-[#333333] bg-[#1a1a1a] flex flex-col overflow-hidden h-full"
              style={{ width: `${aiPanelWidth}px` }}
            >
              <AIPanel
                isMobile={isMobile}
                aiProvider={aiProvider}
                setAiProvider={setAiProvider}
                aiMessages={aiMessages}
                aiLoading={aiLoading}
                aiInput={aiInput}
                setAiInput={setAiInput}
                sendToAI={sendToAI}
                applyAIArt={applyAIArt}
                close={() => setShowAI(false)}
                chatThreads={chatThreads}
                currentThreadId={currentThreadId}
                createNewThread={createNewThread}
                switchToThread={switchToThread}
                clearCurrentThread={clearCurrentThread}
                deleteCurrentThread={deleteCurrentThread}
                updateThreadTitle={updateThreadTitle}
                dockMode={aiDockMode}
                dockAIPanel={dockAIPanel}
                isDocked={true}
              />
            </div>
          </>
        )}

      </div>

      {/* Floating AI Assistant Chat Container */}
      {showAI && aiDockMode === 'floating' && (
        <AIPanel
          isMobile={isMobile}
          aiProvider={aiProvider}
          setAiProvider={setAiProvider}
          aiMessages={aiMessages}
          aiLoading={aiLoading}
          aiInput={aiInput}
          setAiInput={setAiInput}
          sendToAI={sendToAI}
          applyAIArt={applyAIArt}
          close={() => setShowAI(false)}
          // Chat thread management
          chatThreads={chatThreads}
          currentThreadId={currentThreadId}
          createNewThread={createNewThread}
          switchToThread={switchToThread}
          clearCurrentThread={clearCurrentThread}
          deleteCurrentThread={deleteCurrentThread}
          updateThreadTitle={updateThreadTitle}
          // Docking functionality
          dockMode={aiDockMode}
          dockAIPanel={dockAIPanel}
          isDocked={false}
        />
      )}

      {/* Footer */}
      <Footer />
    </div>
  );
}
