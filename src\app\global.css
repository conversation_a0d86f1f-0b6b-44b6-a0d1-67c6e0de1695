@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Ensure proper box-sizing and prevent horizontal overflow */
  *, *::before, *::after {
    box-sizing: border-box;
  }

  html {
    /* Prevent horizontal scrolling */
    overflow-x: hidden;
    /* Ensure full height */
    height: 100%;
    /* Improve text rendering */
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    /* Prevent horizontal scrolling */
    overflow-x: hidden;
    /* Ensure full height */
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
    /* Reset margins */
    margin: 0;
    padding: 0;
    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus on iOS */
  input, textarea, select, button {
    @apply outline-none focus:outline-none;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Ensure responsive images and media */
  img, video, canvas, svg {
    max-width: 100%;
    height: auto;
  }

  /* Improve scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1a1a1a;
  }

  ::-webkit-scrollbar-thumb {
    background: #333333;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #444444;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }

  /* Thin scrollbar for chat container */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #333333 #1a1a1a;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #1a1a1a;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #333333;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #444444;
  }

  /* Responsive text utilities */
  .text-responsive {
    font-size: clamp(0.75rem, 2vw, 1rem);
  }

  .text-responsive-sm {
    font-size: clamp(0.625rem, 1.5vw, 0.875rem);
  }

  /* Responsive container utilities */
  .container-responsive {
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
    padding-left: clamp(0.5rem, 2vw, 1.5rem);
    padding-right: clamp(0.5rem, 2vw, 1.5rem);
  }

  /* Safe area utilities for mobile devices */
  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-inset-right {
    padding-right: env(safe-area-inset-right);
  }
}
