// Simple crawler: fetches a single page and extracts visible text
// Uses a lightweight HTML extraction without heavy dependencies to keep scope minimal

function isHttpUrl(urlString) {
  try {
    const u = new URL(urlString);
    return u.protocol === 'http:' || u.protocol === 'https:';
  } catch {
    return false;
  }
}

function isBlockedHost(hostname) {
  const lower = hostname.toLowerCase();
  if (
    lower === 'localhost' ||
    lower === '127.0.0.1' ||
    lower === '::1'
  ) {
    return true;
  }
  if (lower.endsWith('.local') || lower.endsWith('.lan')) return true;
  return false;
}

function stripHtmlToText(html, maxChars) {
  // Remove scripts/styles/noscript
  let s = html
    .replace(/<script[\s\S]*?<\/script>/gi, '')
    .replace(/<style[\s\S]*?<\/style>/gi, '')
    .replace(/<noscript[\s\S]*?<\/noscript>/gi, '');
  // Replace block tags with newlines
  s = s.replace(/<(\/)?(p|div|br|li|ul|ol|h[1-6]|section|article|header|footer|main|nav|tr|table)\b[^>]*>/gi, '\n');
  // Remove all remaining tags
  s = s.replace(/<[^>]+>/g, '');
  // Decode basic entities
  s = s
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
  // Collapse whitespace
  s = s.replace(/[\t\r ]+/g, ' ').replace(/\n{2,}/g, '\n').trim();
  if (typeof maxChars === 'number' && maxChars > 0) {
    return s.slice(0, maxChars);
  }
  return s;
}

export async function POST(request) {
  try {
    const { url, maxBytes = 400_000, timeoutMs = 12_000, maxChars = 40_000 } = await request.json();
    if (!url || typeof url !== 'string' || !isHttpUrl(url)) {
      return new Response(JSON.stringify({ error: 'Invalid URL' }), { status: 400 });
    }
    const { hostname } = new URL(url);
    if (isBlockedHost(hostname)) {
      return new Response(JSON.stringify({ error: 'Blocked host' }), { status: 403 });
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    let resp;
    try {
      resp = await fetch(url, {
        method: 'GET',
        redirect: 'follow',
        signal: controller.signal,
        headers: { 'user-agent': 'CreateXYZ-Assistant/1.0' },
      });
    } finally {
      clearTimeout(timeout);
    }

    const contentType = resp.headers.get('content-type') || '';
    const isHtml = contentType.includes('text/html');
    if (!isHtml) {
      return new Response(
        JSON.stringify({
          ok: resp.ok,
          status: resp.status,
          contentType,
          message: 'Non-HTML content, use /api/tools/fetch instead',
        }),
        { headers: { 'content-type': 'application/json' } }
      );
    }

    // Read up to maxBytes
    const reader = resp.body?.getReader?.();
    let received = 0;
    let chunks = [];
    let truncated = false;
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        if (value) {
          received += value.byteLength;
          if (received > maxBytes) {
            const remaining = maxBytes - (received - value.byteLength);
            if (remaining > 0) chunks.push(value.subarray(0, remaining));
            truncated = true;
            break;
          }
          chunks.push(value);
        }
      }
    } else {
      const ab = await resp.arrayBuffer();
      const buf = new Uint8Array(ab);
      if (buf.byteLength > maxBytes) {
        chunks = [buf.subarray(0, maxBytes)];
        truncated = true;
        received = maxBytes;
      } else {
        chunks = [buf];
        received = buf.byteLength;
      }
    }

    const merged = new Uint8Array(chunks.reduce((acc, c) => acc + c.byteLength, 0));
    let offset = 0;
    for (const c of chunks) {
      merged.set(c, offset);
      offset += c.byteLength;
    }
    const html = new TextDecoder('utf-8').decode(merged);
    const text = stripHtmlToText(html, maxChars);

    return new Response(
      JSON.stringify({
        ok: resp.ok,
        status: resp.status,
        contentType,
        truncated,
        url: resp.url || url,
        text,
      }),
      { headers: { 'content-type': 'application/json' } }
    );
  } catch (error) {
    return new Response(JSON.stringify({ error: error?.message || 'Crawl failed' }), { status: 500 });
  }
}


