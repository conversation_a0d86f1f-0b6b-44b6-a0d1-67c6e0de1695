import { <PERSON><PERSON>, ChevronDown, Maximize2, MessageSquareP<PERSON>, Minimize2, MoreVertical, PanelLeft, PanelRight, Send, Trash2, X } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

export default function AIPanel({
  isMobile,
  aiProvider,
  setAiProvider,
  aiMessages,
  aiLoading,
  aiInput,
  setAiInput,
  sendToAI,
  applyAIArt,
  close,
  // Chat thread management
  chatThreads,
  currentThreadId,
  createNewThread,
  switchToThread,
  clearCurrentThread,
  deleteCurrentThread,
  updateThreadTitle,
  // Docking functionality
  dockMode = 'floating',
  dockAIPanel,
  isDocked = false,
}) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [position, setPosition] = useState({ x: 20, y: 20 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showThreadDropdown, setShowThreadDropdown] = useState(false);
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showDockingMenu, setShowDockingMenu] = useState(false);
  const panelRef = useRef(null);
  const messagesEndRef = useRef(null);

  // Get current thread info
  const currentThread = currentThreadId && chatThreads ? chatThreads[currentThreadId] : null;
  const threadList = chatThreads ? Object.values(chatThreads).sort((a, b) =>
    new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime()
  ) : [];

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [aiMessages]);

  const handleMouseDown = (e) => {
    if (e.target.closest('.drag-handle')) {
      setIsDragging(true);
      const rect = panelRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;

      // Keep panel within viewport bounds
      const maxX = window.innerWidth - (isMobile ? 320 : 400);
      const maxY = window.innerHeight - (isMinimized ? 60 : 500);

      setPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY)),
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (panelRef.current && !panelRef.current.contains(event.target)) {
        setShowThreadDropdown(false);
        setShowOptionsMenu(false);
        setShowDockingMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Auto-undock on mobile
  useEffect(() => {
    if (isMobile && isDocked && dockAIPanel) {
      dockAIPanel('floating');
    }
  }, [isMobile, isDocked, dockAIPanel]);

  return (
    <div
      ref={panelRef}
      className={`${
        isDocked
          ? 'w-full h-full bg-[#1a1a1a] flex flex-col'
          : `fixed z-50 bg-[#1a1a1a] border border-[#333333] rounded-lg shadow-2xl transition-all duration-300 ${
              isMobile ? 'inset-4' : isMinimized ? 'w-80 h-14' : 'w-96 h-[500px]'
            }`
      }`}
      style={isDocked ? {} : {
        left: isMobile ? undefined : `${position.x}px`,
        top: isMobile ? undefined : `${position.y}px`,
      }}
      onMouseDown={isDocked ? undefined : handleMouseDown}
    >
      {/* Header */}
      <div className={`${isDocked ? '' : 'drag-handle'} border-b border-[#333333] p-3 flex items-center justify-between ${isDocked ? '' : 'cursor-move'} bg-[#1a1a1a] ${isDocked ? '' : 'rounded-t-lg'}`}>
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
            <Bot size={12} className="text-white" />
          </div>
          <div className="flex-1 min-w-0">
            {!isMinimized && (
              <div className="relative">
                <button
                  onClick={() => setShowThreadDropdown(!showThreadDropdown)}
                  className="flex items-center space-x-1 text-white font-bold text-xs hover:text-[#cccccc] transition-colors max-w-full"
                >
                  <span className="truncate">{currentThread?.title || 'New Chat'}</span>
                  <ChevronDown size={10} />
                </button>

                {/* Thread Dropdown */}
                {showThreadDropdown && (
                  <div className="absolute top-full left-0 mt-1 w-48 bg-[#2a2a2a] border border-[#444444] rounded shadow-lg z-50 max-h-48 overflow-y-auto">
                    <div className="p-2 border-b border-[#444444]">
                      <button
                        onClick={() => {
                          createNewThread();
                          setShowThreadDropdown(false);
                        }}
                        className="flex items-center space-x-2 w-full p-2 text-xs text-[#cccccc] hover:bg-[#3a3a3a] rounded transition-colors"
                      >
                        <MessageSquarePlus size={12} />
                        <span>New Chat</span>
                      </button>
                    </div>
                    <div className="max-h-32 overflow-y-auto">
                      {threadList.map((thread) => (
                        <button
                          key={thread.id}
                          onClick={() => {
                            switchToThread(thread.id);
                            setShowThreadDropdown(false);
                          }}
                          className={`w-full p-2 text-left text-xs hover:bg-[#3a3a3a] transition-colors ${
                            thread.id === currentThreadId ? 'bg-[#3a3a3a] text-white' : 'text-[#cccccc]'
                          }`}
                        >
                          <div className="truncate font-medium">{thread.title}</div>
                          <div className="text-[10px] text-[#888888] truncate">
                            {new Date(thread.updatedAt || thread.createdAt).toLocaleDateString()}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
            {isMinimized && (
              <div className="text-white font-bold text-xs truncate">
                {currentThread?.title || 'ASCII Assistant'}
              </div>
            )}
            {!isMinimized && (
              <div className="text-[#888888] text-[10px]">
                {aiProvider === 'anthropic' ? 'Claude 3.5 Sonnet' : 'GPT-4 Turbo'} via Maestra AI
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-1 flex-shrink-0">
          {!isMinimized && !isDocked && (
            <div className="relative">
              <button
                onClick={() => setShowDockingMenu(!showDockingMenu)}
                className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors"
                title="Dock Panel"
              >
                <MoreVertical size={14} />
              </button>

              {/* Docking Menu */}
              {showDockingMenu && (
                <div className="absolute top-full right-0 mt-1 w-32 bg-[#2a2a2a] border border-[#444444] rounded shadow-lg z-50">
                  <button
                    onClick={() => {
                      dockAIPanel('left');
                      setShowDockingMenu(false);
                    }}
                    className="w-full p-2 text-left text-xs text-[#cccccc] hover:bg-[#3a3a3a] transition-colors flex items-center space-x-2"
                  >
                    <PanelLeft size={10} />
                    <span>Dock Left</span>
                  </button>
                  <button
                    onClick={() => {
                      dockAIPanel('right');
                      setShowDockingMenu(false);
                    }}
                    className="w-full p-2 text-left text-xs text-[#cccccc] hover:bg-[#3a3a3a] transition-colors flex items-center space-x-2"
                  >
                    <PanelRight size={10} />
                    <span>Dock Right</span>
                  </button>
                </div>
              )}
            </div>
          )}

          {!isMinimized && (
            <div className="relative">
              <button
                onClick={() => setShowOptionsMenu(!showOptionsMenu)}
                className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors"
                title="Chat Options"
              >
                <MoreVertical size={14} />
              </button>

              {/* Options Menu */}
              {showOptionsMenu && (
                <div className="absolute top-full right-0 mt-1 w-36 bg-[#2a2a2a] border border-[#444444] rounded shadow-lg z-50">
                  <button
                    onClick={() => {
                      clearCurrentThread();
                      setShowOptionsMenu(false);
                    }}
                    className="w-full p-2 text-left text-xs text-[#cccccc] hover:bg-[#3a3a3a] transition-colors"
                  >
                    Clear Chat
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('Delete this chat thread permanently?')) {
                        deleteCurrentThread();
                        setShowOptionsMenu(false);
                      }
                    }}
                    className="w-full p-2 text-left text-xs text-red-400 hover:bg-[#3a3a3a] transition-colors flex items-center space-x-2"
                  >
                    <Trash2 size={10} />
                    <span>Delete Thread</span>
                  </button>
                </div>
              )}
            </div>
          )}

          {isDocked && (
            <button
              onClick={() => dockAIPanel('floating')}
              className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors"
              title="Undock Panel"
            >
              <X size={14} />
            </button>
          )}

          {!isDocked && (
            <>
              <button
                onClick={() => setIsMinimized(!isMinimized)}
                className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors"
                title={isMinimized ? "Maximize" : "Minimize"}
              >
                {isMinimized ? <Maximize2 size={14} /> : <Minimize2 size={14} />}
              </button>
              <button onClick={close} className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors">
                <X size={14} />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Messages - Only show when not minimized */}
      {!isMinimized && (
        <div className="flex-1 overflow-y-auto p-3 space-y-2 scrollbar-thin scrollbar-thumb-[#333333] scrollbar-track-[#1a1a1a]">
          {aiMessages.map((message, index) => (
            <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[85%] rounded-lg p-2 ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-[#2a2a2a] text-[#cccccc]'
              }`}>
                <div className="text-xs whitespace-pre-wrap">{message.content}</div>
                {message.asciiArt && (
                  <div className="mt-2">
                    <div className="bg-[#0a0a0a] p-2 rounded text-green-400 font-mono text-[10px] overflow-x-auto">
                      {message.asciiArt}
                    </div>
                    {message.action === 'apply' && (
                      <button
                        onClick={() => applyAIArt(message.asciiArt)}
                        className="mt-2 px-2 py-1 bg-blue-600 text-white rounded text-[10px] font-bold hover:bg-blue-700 transition-colors"
                      >
                        Apply to Canvas
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
          {aiLoading && (
            <div className="flex justify-start">
              <div className="bg-[#2a2a2a] text-[#cccccc] rounded-lg p-2">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                  <span className="text-xs">Generating...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Input Area - Only show when not minimized */}
      {!isMinimized && (
        <div className="border-t border-[#333333] p-3 flex-shrink-0">
          <div className="mb-2">
            <label className="text-[10px] text-[#888888] mb-1 block">AI Provider</label>
            <select
              value={aiProvider}
              onChange={(e) => setAiProvider(e.target.value)}
              className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-[10px]"
            >
              <option value="openai">OpenAI GPT-4 Turbo</option>
              <option value="anthropic">Anthropic Claude 3.5 Sonnet</option>
            </select>
          </div>

          <div className="flex space-x-2">
            <input
              type="text"
              value={aiInput}
              onChange={(e) => setAiInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && sendToAI()}
              placeholder="Ask me to create ASCII art..."
              className="flex-1 px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs placeholder-[#666666] focus:border-blue-500 focus:outline-none"
            />
            <button
              onClick={sendToAI}
              disabled={!aiInput.trim() || aiLoading}
              className="px-3 py-1 bg-blue-600 text-white rounded font-bold disabled:bg-[#666666] disabled:text-[#333333] hover:bg-blue-700 transition-colors"
            >
              <Send size={12} />
            </button>
          </div>
          <div className="mt-1 text-[10px] text-[#666666]">
            Try: "Make a cat", "Create a heart", "Draw mountains"
            <div className="mt-1 text-[9px] text-[#555555]">
              Requires {aiProvider === 'anthropic' ? 'ANTHROPIC_API_KEY' : 'OPENAI_API_KEY'} env variable
            </div>
          </div>
        </div>
      )}
    </div>
  );
}


