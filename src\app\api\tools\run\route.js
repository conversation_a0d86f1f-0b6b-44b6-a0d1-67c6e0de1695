// Minimal tool runner to support MCP-like JSON tool calls
// Supported tools: "fetch", "crawl"

function isHttpUrl(urlString) {
  try {
    const u = new URL(urlString);
    return u.protocol === 'http:' || u.protocol === 'https:';
  } catch {
    return false;
  }
}

function isBlockedHost(hostname) {
  const lower = hostname.toLowerCase();
  if (lower === 'localhost' || lower === '127.0.0.1' || lower === '::1') return true;
  if (lower.endsWith('.local') || lower.endsWith('.lan')) return true;
  return false;
}

const TEXT_LIKE = [
  'text/plain',
  'text/html',
  'text/css',
  'application/json',
  'application/xml',
  'text/xml',
  'application/javascript',
];

async function runFetchTool({ url, maxBytes = 200_000, timeoutMs = 10_000 }) {
  if (!url || typeof url !== 'string' || !isHttpUrl(url)) {
    return { error: 'Invalid URL' };
  }
  const { hostname } = new URL(url);
  if (isBlockedHost(hostname)) return { error: 'Blocked host' };

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), timeoutMs);
  let resp;
  try {
    resp = await fetch(url, { method: 'GET', redirect: 'follow', signal: controller.signal });
  } finally {
    clearTimeout(timeout);
  }
  const contentType = resp.headers.get('content-type') || '';
  const isTextLike = TEXT_LIKE.some((t) => contentType.includes(t));

  const reader = resp.body?.getReader?.();
  let received = 0;
  let chunks = [];
  let truncated = false;
  if (reader) {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      if (value) {
        received += value.byteLength;
        if (received > maxBytes) {
          const remaining = maxBytes - (received - value.byteLength);
          if (remaining > 0) chunks.push(value.subarray(0, remaining));
          truncated = true;
          break;
        }
        chunks.push(value);
      }
    }
  } else {
    const ab = await resp.arrayBuffer();
    const buf = new Uint8Array(ab);
    if (buf.byteLength > maxBytes) {
      chunks = [buf.subarray(0, maxBytes)];
      truncated = true;
      received = maxBytes;
    } else {
      chunks = [buf];
      received = buf.byteLength;
    }
  }
  const merged = new Uint8Array(chunks.reduce((acc, c) => acc + c.byteLength, 0));
  let offset = 0;
  for (const c of chunks) { merged.set(c, offset); offset += c.byteLength; }
  let bodyText = null;
  let bodyJson = null;
  if (isTextLike) {
    bodyText = new TextDecoder('utf-8').decode(merged);
    if (contentType.includes('application/json')) {
      try { bodyJson = JSON.parse(bodyText); } catch {}
    }
  }
  return {
    ok: resp.ok,
    status: resp.status,
    contentType,
    truncated,
    bytes: merged.byteLength,
    bodyText: bodyText && bodyText.trim().slice(0, maxBytes),
    bodyJson,
    url: resp.url || url,
  };
}

function stripHtmlToText(html, maxChars) {
  let s = html
    .replace(/<script[\s\S]*?<\/script>/gi, '')
    .replace(/<style[\s\S]*?<\/style>/gi, '')
    .replace(/<noscript[\s\S]*?<\/noscript>/gi, '');
  s = s.replace(/<(\/)?(p|div|br|li|ul|ol|h[1-6]|section|article|header|footer|main|nav|tr|table)\b[^>]*>/gi, '\n');
  s = s.replace(/<[^>]+>/g, '');
  s = s
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
  s = s.replace(/[\t\r ]+/g, ' ').replace(/\n{2,}/g, '\n').trim();
  if (typeof maxChars === 'number' && maxChars > 0) return s.slice(0, maxChars);
  return s;
}

async function runCrawlTool({ url, maxBytes = 400_000, timeoutMs = 12_000, maxChars = 40_000 }) {
  if (!url || typeof url !== 'string' || !isHttpUrl(url)) return { error: 'Invalid URL' };
  const { hostname } = new URL(url);
  if (isBlockedHost(hostname)) return { error: 'Blocked host' };
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), timeoutMs);
  let resp;
  try {
    resp = await fetch(url, { method: 'GET', redirect: 'follow', signal: controller.signal, headers: { 'user-agent': 'CreateXYZ-Assistant/1.0' } });
  } finally {
    clearTimeout(timeout);
  }
  const contentType = resp.headers.get('content-type') || '';
  if (!contentType.includes('text/html')) {
    return { ok: resp.ok, status: resp.status, contentType, message: 'Non-HTML content' };
  }
  const reader = resp.body?.getReader?.();
  let received = 0, chunks = [], truncated = false;
  if (reader) {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      if (value) {
        received += value.byteLength;
        if (received > maxBytes) {
          const remaining = maxBytes - (received - value.byteLength);
          if (remaining > 0) chunks.push(value.subarray(0, remaining));
          truncated = true;
          break;
        }
        chunks.push(value);
      }
    }
  } else {
    const ab = await resp.arrayBuffer();
    const buf = new Uint8Array(ab);
    if (buf.byteLength > maxBytes) { chunks = [buf.subarray(0, maxBytes)]; truncated = true; received = maxBytes; }
    else { chunks = [buf]; received = buf.byteLength; }
  }
  const merged = new Uint8Array(chunks.reduce((acc, c) => acc + c.byteLength, 0));
  let offset = 0; for (const c of chunks) { merged.set(c, offset); offset += c.byteLength; }
  const html = new TextDecoder('utf-8').decode(merged);
  const text = stripHtmlToText(html, maxChars);
  return { ok: resp.ok, status: resp.status, contentType, truncated, url: resp.url || url, text };
}

export async function POST(request) {
  try {
    const { toolId, args } = await request.json();
    if (typeof toolId !== 'string') {
      return new Response(JSON.stringify({ error: 'toolId required' }), { status: 400 });
    }
    if (toolId === 'fetch') {
      const result = await runFetchTool(args || {});
      return new Response(JSON.stringify(result), { headers: { 'content-type': 'application/json' } });
    }
    if (toolId === 'crawl') {
      const result = await runCrawlTool(args || {});
      return new Response(JSON.stringify(result), { headers: { 'content-type': 'application/json' } });
    }
    return new Response(JSON.stringify({ error: 'Unknown toolId' }), { status: 404 });
  } catch (error) {
    return new Response(JSON.stringify({ error: error?.message || 'Tool run failed' }), { status: 500 });
  }
}


