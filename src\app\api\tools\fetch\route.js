// Minimal safe external fetch tool
// - Supports GET only
// - Blocks localhost and private networks
// - Limits response size and time

const TEXT_LIKE = [
  'text/plain',
  'text/html',
  'text/css',
  'application/json',
  'application/xml',
  'text/xml',
  'application/javascript',
];

function isHttpUrl(urlString) {
  try {
    const u = new URL(urlString);
    return u.protocol === 'http:' || u.protocol === 'https:';
  } catch {
    return false;
  }
}

function isBlockedHost(hostname) {
  const lower = hostname.toLowerCase();
  if (
    lower === 'localhost' ||
    lower === '127.0.0.1' ||
    lower === '::1'
  ) {
    return true;
  }
  // Block common private ranges
  if (
    lower.endsWith('.local') ||
    lower.endsWith('.lan')
  ) {
    return true;
  }
  return false;
}

export async function POST(request) {
  try {
    const { url, maxBytes = 200_000, timeoutMs = 10_000 } = await request.json();

    if (!url || typeof url !== 'string' || !isHttpUrl(url)) {
      return new Response(JSON.stringify({ error: 'Invalid URL' }), { status: 400 });
    }

    const { hostname } = new URL(url);
    if (isBlockedHost(hostname)) {
      return new Response(JSON.stringify({ error: 'Blocked host' }), { status: 403 });
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    let resp;
    try {
      resp = await fetch(url, {
        method: 'GET',
        redirect: 'follow',
        signal: controller.signal,
      });
    } finally {
      clearTimeout(timeout);
    }

    const contentType = resp.headers.get('content-type') || '';
    const isTextLike = TEXT_LIKE.some((t) => contentType.includes(t));

    // Read up to maxBytes
    const reader = resp.body?.getReader?.();
    let received = 0;
    let chunks = [];
    let truncated = false;
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        if (value) {
          received += value.byteLength;
          if (received > maxBytes) {
            const remaining = maxBytes - (received - value.byteLength);
            if (remaining > 0) {
              chunks.push(value.subarray(0, remaining));
            }
            truncated = true;
            break;
          }
          chunks.push(value);
        }
      }
    } else {
      const ab = await resp.arrayBuffer();
      const buf = new Uint8Array(ab);
      if (buf.byteLength > maxBytes) {
        chunks = [buf.subarray(0, maxBytes)];
        truncated = true;
        received = maxBytes;
      } else {
        chunks = [buf];
        received = buf.byteLength;
      }
    }

    const merged = new Uint8Array(chunks.reduce((acc, c) => acc + c.byteLength, 0));
    let offset = 0;
    for (const c of chunks) {
      merged.set(c, offset);
      offset += c.byteLength;
    }

    let bodyText = null;
    let bodyJson = null;
    if (isTextLike) {
      bodyText = new TextDecoder('utf-8').decode(merged);
      if (contentType.includes('application/json')) {
        try {
          bodyJson = JSON.parse(bodyText);
        } catch {}
      }
    }

    return new Response(
      JSON.stringify({
        ok: resp.ok,
        status: resp.status,
        contentType,
        truncated,
        bytes: merged.byteLength,
        bodyText: bodyText && bodyText.trim().slice(0, maxBytes),
        bodyJson,
        url: resp.url || url,
      }),
      { headers: { 'content-type': 'application/json' } }
    );
  } catch (error) {
    return new Response(JSON.stringify({ error: error?.message || 'Fetch failed' }), { status: 500 });
  }
}


